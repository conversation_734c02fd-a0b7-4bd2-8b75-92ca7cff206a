import { useState } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { toast } from 'react-hot-toast';
import {
  ArrowLeftIcon,
  ArrowPathIcon,
  PlayIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  StopIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { invoiceProcessingApi } from '../services/api';
import { usePermissions } from '../hooks/usePermissions';
import { useAuth } from '../contexts/AuthContext';
import { ProcessingStep, SessionLog } from '../types';
import '../appCustomStyles.css';

export default function SessionDetailPage() {
  const { sessionId } = useParams<{ sessionId: string }>();
  const { hasPermission } = usePermissions();
  const { refreshUser } = useAuth();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [expandedLogs, setExpandedLogs] = useState<Set<string>>(new Set());

  const { data: sessionDetail, isLoading, error } = useQuery(
    ['session-detail', sessionId],
    () => invoiceProcessingApi.getSessionDetail(sessionId!),
    { enabled: !!sessionId }
  );

  const retryMutation = useMutation(
    ({ fromStep }: { fromStep?: ProcessingStep }) => 
      invoiceProcessingApi.retrySession(sessionId!, fromStep),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['session-detail', sessionId]);
        toast.success('Session retry started successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to retry session');
      },
    }
  );

  const executeStepMutation = useMutation(
    (stepName: ProcessingStep) =>
      invoiceProcessingApi.executeStep(sessionId!, stepName),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['session-detail', sessionId]);
        toast.success('Step execution started successfully');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to execute step');
      },
    }
  );

  const deleteSessionMutation = useMutation(
    () => invoiceProcessingApi.deleteSession(sessionId!),
    {
      onSuccess: () => {
        toast.success('Session and related invoice deleted successfully');
        navigate('/sessions');
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.detail || 'Failed to delete session');
      },
    }
  );

  const handleDeleteSession = async () => {
    const isProcessing = session?.status === 'processing';

    const confirmMessage = isProcessing
      ? `Är du säker på att du vill stoppa körningen och radera denna session?

Detta kommer att:
- Stoppa pågående bearbetning omedelbart
- Radera sessionen och alla dess loggar
- Radera tillhörande faktura permanent

Denna åtgärd kan inte ångras.`
      : `Är du säker på att du vill radera denna session?

Detta kommer att:
- Radera sessionen och alla dess loggar
- Radera tillhörande faktura permanent

Denna åtgärd kan inte ångras.`;

    if (window.confirm(confirmMessage)) {
      deleteSessionMutation.mutate();
    }
  };

  const toggleLogExpansion = (logId: string) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'processing':
        return <ClockIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      case 'failed':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStepStatus = (stepName: string, logs: SessionLog[]) => {
    const stepLogs = logs.filter(log => log.step_name === stepName);
    if (stepLogs.length === 0) return 'pending';
    
    const latestLog = stepLogs[stepLogs.length - 1];
    return latestLog.success ? 'completed' : 'failed';
  };

  const formatExecutionTime = (ms?: number) => {
    if (!ms) return 'N/A';
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error || !sessionDetail) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading session</h3>
        <p className="mt-1 text-sm text-gray-500">
          {error instanceof Error ? error.message : 'Session not found'}
        </p>
        <Link
          to="/sessions"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200"
        >
          <ArrowLeftIcon className="mr-2 h-4 w-4" />
          Back to Sessions
        </Link>
      </div>
    );
  }

  const { session, invoice, processing_results, logs, statistics } = sessionDetail;
  const steps: ProcessingStep[] = ['extrahera', 'kontext', 'hitta_konto', 'bokfora'];

  return (
    <div className="page-background">
      <div className="content-container">
        {/* Header */}
        <div className="card-container-large mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                to="/sessions"
                className="text-gray-400 hover:text-gray-600"
              >
                <ArrowLeftIcon className="h-6 w-6" />
              </Link>
              <div>
                <h1 className="page-title custom-gradient-text">Session Details</h1>
                <p className="text-gray-700">
                  Session ID: {session.id}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              {/* Debug - temporary */}
              <div style={{ fontSize: '10px', background: 'yellow', padding: '2px' }}>
                Status: "{session.status}" | HasPerm: {hasPermission('invoices:write') ? 'YES' : 'NO'}
              </div>

              {/* Refresh Permissions Button */}
              <button
                onClick={async () => {
                  await refreshUser();
                  toast.success('Permissions refreshed!');
                }}
                className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600"
                title="Refresh user permissions from server"
              >
                🔄 Refresh Perms
              </button>

              {session.status === 'failed' && hasPermission('invoices:write') && (
                <button
                  onClick={() => retryMutation.mutate({})}
                  disabled={retryMutation.isLoading}
                  className="btn-secondary"
                >
                  <ArrowPathIcon className="mr-2 h-4 w-4" />
                  Retry Session
                </button>
              )}

              {session.status === 'processing' && hasPermission('invoices:write') && (
                <button
                  onClick={handleDeleteSession}
                  disabled={deleteSessionMutation.isLoading}
                  className="btn-danger"
                >
                  <StopIcon className="mr-2 h-4 w-4" />
                  {deleteSessionMutation.isLoading ? 'Stopping...' : 'Stop & Delete'}
                </button>
              )}

              {(session.status === 'pending' || session.status === 'completed' || session.status === 'failed') && hasPermission('invoices:write') && (
                <button
                  onClick={handleDeleteSession}
                  disabled={deleteSessionMutation.isLoading}
                  className="btn-danger"
                >
                  <TrashIcon className="mr-2 h-4 w-4" />
                  {deleteSessionMutation.isLoading ? 'Deleting...' : 'Delete Session'}
                </button>
              )}

              {/* Test button - should always show if has permission */}
              <button
                onClick={handleDeleteSession}
                disabled={deleteSessionMutation.isLoading}
                style={{
                  backgroundColor: hasPermission('invoices:write') ? 'green' : 'red',
                  color: 'white',
                  padding: '4px 8px',
                  fontSize: '12px'
                }}
              >
                TEST: {hasPermission('invoices:write') ? 'HAS PERM' : 'NO PERM'}
              </button>
            </div>
          </div>
        </div>

        {/* Session Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Session Info */}
          <div className="card-container">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Information</h3>
          <dl className="space-y-3">
            <div>
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 flex items-center">
                {getStatusIcon(session.status)}
                <span className="ml-2 text-sm text-gray-900">{session.status}</span>
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Current Step</dt>
              <dd className="mt-1 text-sm text-gray-900">{session.current_step || 'None'}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Created</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(session.created_at).toLocaleString()}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Updated</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {new Date(session.updated_at).toLocaleString()}
              </dd>
            </div>
            {session.error_message && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Error</dt>
                <dd className="mt-1 text-sm text-red-600">{session.error_message}</dd>
              </div>
            )}
          </dl>
        </div>

        {/* Invoice Info */}
        {invoice && (
          <div className="card-container">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Invoice Information</h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Import Type</dt>
                <dd className="mt-1 text-sm text-gray-900">{invoice.import_typ}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Supplier</dt>
                <dd className="mt-1 text-sm text-gray-900">{invoice.supplier_name || 'Unknown'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Filename</dt>
                <dd className="mt-1 text-sm text-gray-900">{invoice.original_filename || 'N/A'}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Status</dt>
                <dd className="mt-1 text-sm text-gray-900">{invoice.status}</dd>
              </div>
            </dl>
          </div>
        )}

        {/* Statistics */}
        <div className="card-container">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Statistics</h3>
          <dl className="space-y-3">
            <div>
              <dt className="text-sm font-medium text-gray-500">Total Steps</dt>
              <dd className="mt-1 text-sm text-gray-900">{statistics.total_steps}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Successful</dt>
              <dd className="mt-1 text-sm text-green-600">{statistics.successful_steps}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Failed</dt>
              <dd className="mt-1 text-sm text-red-600">{statistics.failed_steps}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Total Time</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatExecutionTime(statistics.total_execution_time_ms)}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Average Time</dt>
              <dd className="mt-1 text-sm text-gray-900">
                {formatExecutionTime(statistics.average_execution_time_ms)}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      {/* Processing Steps */}
      <div className="card-container">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Processing Steps</h3>
        <div className="space-y-4">
          {steps.map((step) => {
            const stepStatus = getStepStatus(step, logs);
            const stepLogs = logs.filter(log => log.step_name === step);
            
            return (
              <div key={step} className="border rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {getStatusIcon(stepStatus)}
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 capitalize">
                        {step.replace('_', ' ')}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {stepLogs.length} log{stepLogs.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {stepStatus === 'failed' && hasPermission('invoices:write') && (
                      <button
                        onClick={() => executeStepMutation.mutate(step)}
                        disabled={executeStepMutation.isLoading}
                        className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                      >
                        <PlayIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
                
                {/* Step Results */}
                {stepStatus === 'completed' && (
                  <div className="mt-4 space-y-3">
                    {step === 'extrahera' && processing_results.extracted_data && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700">Extracted Data</h5>
                        <div className="mt-1 p-3 bg-gray-50 rounded text-sm">
                          <pre className="whitespace-pre-wrap">{processing_results.extracted_data}</pre>
                        </div>
                      </div>
                    )}
                    {step === 'kontext' && processing_results.context_data && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700">Context Data</h5>
                        <div className="mt-1 p-3 bg-gray-50 rounded text-sm">
                          <pre className="whitespace-pre-wrap">{processing_results.context_data}</pre>
                        </div>
                      </div>
                    )}
                    {step === 'hitta_konto' && processing_results.account_data && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700">Account Data</h5>
                        <div className="mt-1 p-3 bg-gray-50 rounded text-sm">
                          <pre className="whitespace-pre-wrap">
                            {JSON.stringify(processing_results.account_data, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                    {step === 'bokfora' && processing_results.booking_result && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700">Booking Result</h5>
                        <div className="mt-1 p-3 bg-gray-50 rounded text-sm">
                          <pre className="whitespace-pre-wrap">
                            {JSON.stringify(processing_results.booking_result, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Execution Logs */}
      <div className="card-container">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Execution Logs</h3>
        <div className="space-y-4">
          {logs.map((log) => (
            <div key={log.id} className="border rounded-lg">
              <div
                className="p-4 cursor-pointer hover:bg-gray-50"
                onClick={() => toggleLogExpansion(log.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {log.success ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 capitalize">
                        {log.step_name.replace('_', ' ')}
                      </h4>
                      <p className="text-sm text-gray-500">
                        {new Date(log.created_at).toLocaleString()} • 
                        {formatExecutionTime(log.execution_time_ms)}
                      </p>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    {expandedLogs.has(log.id) ? (
                      <ChevronDownIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronRightIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>
              
              {expandedLogs.has(log.id) && (
                <div className="border-t p-4 space-y-4">
                  <div>
                    <h5 className="text-sm font-medium text-gray-700">Prompt Sent</h5>
                    <div className="mt-1 p-3 bg-gray-50 rounded text-sm">
                      <pre className="whitespace-pre-wrap">{log.prompt_sent}</pre>
                    </div>
                  </div>
                  
                  <div>
                    <h5 className="text-sm font-medium text-gray-700">LLM Response</h5>
                    <div className="mt-1 p-3 bg-gray-50 rounded text-sm">
                      <pre className="whitespace-pre-wrap">{log.llm_response}</pre>
                    </div>
                  </div>
                  
                  {log.reasoning && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700">Reasoning</h5>
                      <div className="mt-1 p-3 bg-blue-50 rounded text-sm">
                        <pre className="whitespace-pre-wrap">{log.reasoning}</pre>
                      </div>
                    </div>
                  )}
                  
                  {log.error_message && (
                    <div>
                      <h5 className="text-sm font-medium text-gray-700">Error</h5>
                      <div className="mt-1 p-3 bg-red-50 rounded text-sm text-red-600">
                        {log.error_message}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
          
          {logs.length === 0 && (
            <div className="empty-state">
              <ClockIcon className="empty-state-icon" />
              <h3 className="empty-state-title">No logs yet</h3>
              <p className="empty-state-description">
                Logs will appear here as the session processes.
              </p>
            </div>
          )}
        </div>
      </div>
      </div>
    </div>
  );
}
